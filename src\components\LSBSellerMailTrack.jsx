import React, { useState, useEffect } from 'react';
import { Mail, RefreshCw, LogOut, User } from 'lucide-react';

// Import components
import Navigation from './Navigation';
import AccountsTab from './AccountsTab';
import FiltersTab from './FiltersTab';
import ScanTab from './ScanTab';
import DataTab from './DataTab';

// Import API utilities
import { apiCall } from '../utils/api';
import { authUtils } from '../utils/auth';

const LSBSellerMailTrack = () => {
  // Global state management - ✅ UPDATED TO PERSIST TAB STATE
  const [accounts, setAccounts] = useState([]);
  const [filters, setFilters] = useState([]);
  const [scanResults, setScanResults] = useState([]);
  
  // Load active tab from localStorage or default to 'accounts'
  const [activeTab, setActiveTab] = useState(() => {
    return localStorage.getItem('lsb-active-tab') || 'accounts';
  });
  
  const [loading, setLoading] = useState(false);
  const [extractedData, setExtractedData] = useState([]);
  const [lastScanResponse, setLastScanResponse] = useState(null);

  // Save active tab to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem('lsb-active-tab', activeTab);
  }, [activeTab]);

  // Load data on component mount
  useEffect(() => {
    loadAccounts();
    loadFilters();
  }, []);

  // Account API functions
  const loadAccounts = async () => {
    try {
      setLoading(true);
      const data = await apiCall('/Accounts');
      setAccounts(Array.isArray(data) ? data : []);
    } catch (error) {
      console.error('Load accounts error:', error);
      alert('Lỗi khi tải danh sách tài khoản: ' + error.message);
      setAccounts([]);
    } finally {
      setLoading(false);
    }
  };

  // Filter API functions
  const loadFilters = async () => {
    try {
      setLoading(true);
      const data = await apiCall('/filters');
      setFilters(Array.isArray(data) ? data : []);
    } catch (error) {
      console.error('Load filters error:', error);
      alert('Lỗi khi tải danh sách bộ lọc: ' + error.message);
      setFilters([]);
    } finally {
      setLoading(false);
    }
  };

  // Calculate total helper
  const calculateTotal = () => {
    return extractedData.reduce((total, item) => {
      const amount = parseFloat(item.amount || 0);
      return total + amount;
    }, 0);
  };

  // Clear processed emails
  const clearProcessedEmails = async () => {
    if (!confirm('Bạn có chắc chắn muốn xóa tất cả dữ liệu đã xử lý?')) {
      return;
    }
    
    try {
      setLoading(true);
      await apiCall('/clear-processed-emails', { method: 'POST' });
      setExtractedData([]);
      setScanResults([]);
      setLastScanResponse(null);
      alert('Đã xóa tất cả dữ liệu thành công!');
    } catch (error) {
      console.error('Clear processed emails error:', error);
      alert('Lỗi khi xóa dữ liệu: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  // Batch process accounts
  const batchProcessAccounts = async (accountIds, fromDate, toDate) => {
    try {
      setLoading(true);
      const response = await apiCall('/batch-process', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          accountIds,
          fromDate,
          toDate
        })
      });
      
      return response;
    } catch (error) {
      console.error('Batch process error:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Get account statistics
  const getAccountStats = async (accountId) => {
    try {
      const stats = await apiCall(`/Accounts/${accountId}/stats`);
      return stats;
    } catch (error) {
      console.error(`Failed to get stats for account ${accountId}:`, error);
      return null;
    }
  };

  // Export all data
  const exportAllData = async () => {
    try {
      setLoading(true);
      const response = await apiCall('/export-all');
      
      // Create and download file
      const blob = new Blob([JSON.stringify(response, null, 2)], { type: 'application/json' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `lsb-seller-data-export-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      
      alert('Dữ liệu đã được xuất thành công!');
    } catch (error) {
      console.error('Export all data error:', error);
      alert('Lỗi khi xuất dữ liệu: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  // Handle logout
  const handleLogout = async () => {
    if (confirm('Bạn có chắc chắn muốn đăng xuất?')) {
      await authUtils.logout();
    }
  };

  // Shared props for child components
  const sharedProps = {
    loading,
    setLoading,
    accounts,
    filters,
    extractedData,
    setExtractedData,
    scanResults,
    setScanResults,
    lastScanResponse,
    setLastScanResponse,
    loadAccounts,
    loadFilters,
    calculateTotal,
    clearProcessedEmails,
    batchProcessAccounts,
    getAccountStats,
    exportAllData,
    setActiveTab
  };

  const renderActiveTab = () => {
    switch (activeTab) {
      case 'accounts':
        return <AccountsTab {...sharedProps} />;
      case 'filters':
        return <FiltersTab {...sharedProps} />;
      case 'scan':
        return <ScanTab {...sharedProps} />;
      case 'data':
        return <DataTab {...sharedProps} />;
      default:
        return <AccountsTab {...sharedProps} />;
    }
  };

  return (
    <div className="min-h-screen w-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <div className="w-full bg-white shadow-lg border-b">
        <div className="w-full px-8 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="bg-gradient-to-r from-blue-600 to-indigo-600 p-3 rounded-lg">
                <Mail className="h-8 w-8 text-white" />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-gray-900">LSB Email Manager</h1>
              </div>
            </div>
            
            {/* Header Stats & User Menu */}
            <div className="flex items-center space-x-6">
              {loading && (
                <div className="flex items-center space-x-2 text-blue-600">
                  <RefreshCw className="h-5 w-5 animate-spin" />
                  <span>Đang xử lý...</span>
                </div>
              )}
              
              {/* Quick Stats */}
              <div className="text-right">
                <p className="text-sm text-gray-500">Tài khoản đã kết nối</p>
                <p className="text-2xl font-bold text-blue-600">{accounts.length}</p>
              </div>
              
              {extractedData.length > 0 && (
                <div className="text-right">
                  <p className="text-sm text-gray-500">Tổng số tiền</p>
                  <p className="text-2xl font-bold text-green-600">
                    {calculateTotal().toLocaleString('vi-VN')} USD
                  </p>
                </div>
              )}

              {/* User Menu */}
              <div className="flex items-center space-x-3">
                <div className="flex items-center space-x-2 text-gray-600">
                  <User className="h-5 w-5" />
                  <span className="text-sm">Admin</span>
                </div>
                <button
                  onClick={handleLogout}
                  className="bg-red-100 text-red-700 px-3 py-2 rounded-lg hover:bg-red-200 transition-colors flex items-center space-x-2"
                  title="Đăng xuất"
                >
                  <LogOut className="h-4 w-4" />
                  <span className="text-sm">Đăng xuất</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <Navigation activeTab={activeTab} setActiveTab={setActiveTab} />

      {/* Content */}
      <div className="w-full px-8 py-8">
        {renderActiveTab()}
      </div>

      {/* Global Loading Overlay */}
      {loading && (
        <div className="fixed inset-0 bg-black bg-opacity-20 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 flex items-center space-x-4">
            <RefreshCw className="h-6 w-6 animate-spin text-blue-600" />
            <span className="text-gray-700">Đang xử lý...</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default LSBSellerMailTrack;
