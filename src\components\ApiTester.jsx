import React, { useState } from 'react';
import { Play, Copy, Check, X } from 'lucide-react';
import { apiCall } from '../utils/api';

const ApiTester = () => {
  const [selectedEndpoint, setSelectedEndpoint] = useState('');
  const [requestBody, setRequestBody] = useState('');
  const [response, setResponse] = useState(null);
  const [loading, setLoading] = useState(false);
  const [copied, setCopied] = useState(false);

  const endpoints = [
    { method: 'GET', path: '/health', description: 'Health Check (Test kết nối)' },
    { method: 'POST', path: '/Auth/login', description: 'Đăng nhập' },
    { method: 'POST', path: '/Auth/logout', description: 'Đăng xuất' },
    { method: 'POST', path: '/Auth/refresh-jwt-token', description: 'Refresh token' },
    { method: 'GET', path: '/Accounts', description: '<PERSON><PERSON><PERSON> danh sách tài khoản' },
    { method: 'POST', path: '/Accounts', description: 'Tạo tài khoản mới' },
    { method: 'PUT', path: '/Accounts/{id}', description: 'Cập nhật tài khoản' },
    { method: 'DELETE', path: '/Accounts/{id}', description: 'Xóa tài khoản' },
    { method: 'GET', path: '/OAuthCallback', description: 'OAuth Callback' },
    { method: 'GET', path: '/auth-url', description: 'Get Auth URL' },
    { method: 'POST', path: '/auth/get-auth-link', description: 'Get Auth Link' },
  ];

  const sampleBodies = {
    '/Auth/login': JSON.stringify({
      request: {
        email: "<EMAIL>",
        password: "password123"
      }
    }, null, 2),
    '/Auth/logout': JSON.stringify({
      request: {}
    }, null, 2),
    '/Auth/refresh-jwt-token': JSON.stringify({
      request: {
        refreshToken: "string"
      }
    }, null, 2),
    '/Accounts': JSON.stringify({
      request: {
        email: "<EMAIL>",
        password: "password123",
        imapServer: "imap.gmail.com",
        imapPort: 993,
        useSSL: true,
        displayName: "Test Account"
      }
    }, null, 2),
    '/auth/get-auth-link': JSON.stringify({
      request: {
        email: "<EMAIL>"
      }
    }, null, 2)
  };

  const handleEndpointChange = (endpoint) => {
    setSelectedEndpoint(endpoint);
    setRequestBody(sampleBodies[endpoint.path] || '');
    setResponse(null);
  };

  const executeRequest = async () => {
    if (!selectedEndpoint) return;

    setLoading(true);
    try {
      let body = undefined;
      if (selectedEndpoint.method !== 'GET' && requestBody.trim()) {
        try {
          // Validate JSON before sending
          JSON.parse(requestBody);
          body = requestBody;
        } catch (jsonError) {
          throw new Error('Invalid JSON format: ' + jsonError.message);
        }
      }

      // Log request details for debugging
      console.log('API Request:', {
        endpoint: selectedEndpoint.path,
        method: selectedEndpoint.method,
        body: body
      });

      const result = await apiCall(selectedEndpoint.path, {
        method: selectedEndpoint.method,
        body
      });

      setResponse({
        success: true,
        data: result,
        timestamp: new Date().toISOString(),
        request: {
          endpoint: selectedEndpoint.path,
          method: selectedEndpoint.method,
          body: body ? JSON.parse(body) : undefined
        }
      });
    } catch (error) {
      console.error('API Test Error:', error);

      setResponse({
        success: false,
        error: error.message,
        timestamp: new Date().toISOString(),
        request: {
          endpoint: selectedEndpoint.path,
          method: selectedEndpoint.method,
          body: requestBody ? (() => {
            try { return JSON.parse(requestBody); } catch { return requestBody; }
          })() : undefined
        }
      });
    } finally {
      setLoading(false);
    }
  };

  const copyResponse = () => {
    if (response) {
      navigator.clipboard.writeText(JSON.stringify(response, null, 2));
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-lg p-6">
      <h2 className="text-xl font-bold mb-4">API Tester</h2>

      {/* API Info */}
      <div className="mb-4 p-3 bg-gray-50 rounded-lg">
        <div className="text-sm text-gray-600">
          <strong>API Base URL:</strong> {import.meta.env.VITE_API_BASE_URL || 'https://localhost:7126/api'}
        </div>
        <div className="text-xs text-gray-500 mt-1">
          Có thể thay đổi trong file .env với biến VITE_API_BASE_URL
        </div>
      </div>
      
      {/* Endpoint Selection */}
      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Chọn API Endpoint
        </label>
        <select
          value={selectedEndpoint ? `${selectedEndpoint.method}:${selectedEndpoint.path}` : ''}
          onChange={(e) => {
            const [method, path] = e.target.value.split(':');
            const endpoint = endpoints.find(ep => ep.method === method && ep.path === path);
            handleEndpointChange(endpoint);
          }}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="">-- Chọn endpoint --</option>
          {endpoints.map((endpoint, index) => (
            <option key={index} value={`${endpoint.method}:${endpoint.path}`}>
              {endpoint.method} {endpoint.path} - {endpoint.description}
            </option>
          ))}
        </select>
      </div>

      {/* Request Body */}
      {selectedEndpoint && selectedEndpoint.method !== 'GET' && (
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Request Body (JSON)
          </label>
          <div className="mb-2 p-2 bg-blue-50 border border-blue-200 rounded text-sm text-blue-700">
            <strong>Lưu ý:</strong> API yêu cầu request body phải có format: <code>{"{ request: { ... } }"}</code>
          </div>
          <textarea
            value={requestBody}
            onChange={(e) => setRequestBody(e.target.value)}
            className="w-full h-32 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-sm"
            placeholder="Nhập JSON request body..."
          />
        </div>
      )}

      {/* Execute Button */}
      <div className="mb-4 flex space-x-2">
        <button
          onClick={executeRequest}
          disabled={!selectedEndpoint || loading}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 flex items-center space-x-2"
        >
          {loading ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              <span>Đang thực hiện...</span>
            </>
          ) : (
            <>
              <Play className="h-4 w-4" />
              <span>Thực hiện</span>
            </>
          )}
        </button>

        {response && (
          <button
            onClick={() => setResponse(null)}
            className="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 transition-colors flex items-center space-x-2"
          >
            <X className="h-4 w-4" />
            <span>Clear</span>
          </button>
        )}
      </div>

      {/* Response */}
      {response && (
        <div className="border rounded-lg">
          <div className="flex items-center justify-between p-3 border-b bg-gray-50">
            <div className="flex items-center space-x-2">
              {response.success ? (
                <Check className="h-5 w-5 text-green-600" />
              ) : (
                <X className="h-5 w-5 text-red-600" />
              )}
              <span className="font-medium">
                {response.success ? 'Thành công' : 'Lỗi'}
              </span>
              <span className="text-sm text-gray-500">
                {new Date(response.timestamp).toLocaleTimeString()}
              </span>
            </div>
            <button
              onClick={copyResponse}
              className="text-gray-600 hover:text-gray-800"
              title="Copy response"
            >
              {copied ? <Check className="h-4 w-4 text-green-600" /> : <Copy className="h-4 w-4" />}
            </button>
          </div>
          <pre className="p-3 text-sm overflow-auto max-h-64 bg-gray-50">
            {JSON.stringify(response, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
};

export default ApiTester;
