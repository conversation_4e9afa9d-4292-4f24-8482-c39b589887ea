import React, { useState } from 'react';
import { Play, Copy, Check, X } from 'lucide-react';
import { apiCall } from '../utils/api';

const ApiTester = () => {
  const [selectedEndpoint, setSelectedEndpoint] = useState('');
  const [requestBody, setRequestBody] = useState('');
  const [response, setResponse] = useState(null);
  const [loading, setLoading] = useState(false);
  const [copied, setCopied] = useState(false);

  const endpoints = [
    { method: 'GET', path: '/health', description: 'Health Check (Test kết nối)' },
    { method: 'POST', path: '/Auth/login', description: 'Đăng nhập' },
    { method: 'POST', path: '/Auth/logout', description: 'Đăng xuất' },
    { method: 'POST', path: '/Auth/refresh-jwt-token', description: 'Refresh token' },
    { method: 'GET', path: '/Accounts', description: '<PERSON><PERSON><PERSON> danh sách tài khoản' },
    { method: 'POST', path: '/Accounts', description: 'Tạo tài khoản mới' },
    { method: 'PUT', path: '/Accounts/{id}', description: 'Cập nhật tài khoản' },
    { method: 'DELETE', path: '/Accounts/{id}', description: 'Xóa tài khoản' },
    { method: 'GET', path: '/OAuthCallback', description: 'OAuth Callback' },
    { method: 'GET', path: '/auth-url', description: 'Get Auth URL' },
    { method: 'POST', path: '/auth/get-auth-link', description: 'Get Auth Link' },
  ];

  const sampleBodies = {
    '/Auth/login': JSON.stringify({
      request: {
        email: "<EMAIL>",
        password: "password123"
      }
    }, null, 2),
    '/Auth/logout': JSON.stringify({
      request: {}
    }, null, 2),
    '/Auth/refresh-jwt-token': JSON.stringify({
      request: {
        refreshToken: "string"
      }
    }, null, 2),
    '/Accounts': JSON.stringify({
      request: {
        email: "<EMAIL>",
        password: "password123",
        imapServer: "imap.gmail.com",
        imapPort: 993,
        useSSL: true,
        displayName: "Test Account"
      }
    }, null, 2),
    '/auth/get-auth-link': JSON.stringify({
      request: {
        email: "<EMAIL>"
      }
    }, null, 2)
  };

  const handleEndpointChange = (endpoint) => {
    setSelectedEndpoint(endpoint);
    setRequestBody(sampleBodies[endpoint.path] || '');
    setResponse(null);
  };

  const executeRequest = async () => {
    if (!selectedEndpoint) return;

    setLoading(true);
    try {
      let body = undefined;
      if (selectedEndpoint.method !== 'GET' && requestBody.trim()) {
        try {
          // Validate JSON before sending
          JSON.parse(requestBody);
          body = requestBody;
        } catch (jsonError) {
          throw new Error('Invalid JSON format: ' + jsonError.message);
        }
      }

      // Log request details for debugging
      console.log('API Request:', {
        endpoint: selectedEndpoint.path,
        method: selectedEndpoint.method,
        body: body
      });

      const result = await apiCall(selectedEndpoint.path, {
        method: selectedEndpoint.method,
        body
      });

      setResponse({
        success: true,
        data: result,
        timestamp: new Date().toISOString(),
        request: {
          endpoint: selectedEndpoint.path,
          method: selectedEndpoint.method,
          body: body ? JSON.parse(body) : undefined
        }
      });
    } catch (error) {
      console.error('API Test Error:', error);

      setResponse({
        success: false,
        error: error.message,
        timestamp: new Date().toISOString(),
        request: {
          endpoint: selectedEndpoint.path,
          method: selectedEndpoint.method,
          body: requestBody ? (() => {
            try { return JSON.parse(requestBody); } catch { return requestBody; }
          })() : undefined
        }
      });
    } finally {
      setLoading(false);
    }
  };

  const copyResponse = () => {
    if (response) {
      navigator.clipboard.writeText(JSON.stringify(response, null, 2));
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    }
  };

  return (
    <div className="bg-white rounded-2xl shadow-2xl p-4 sm:p-6 lg:p-8 border border-gray-100">
      <h2 className="text-2xl font-bold mb-6 bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
        🔧 API Tester
      </h2>

      {/* API Info */}
      <div className="mb-6 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl border border-blue-200">
        <div className="text-sm text-gray-700">
          <div className="flex items-center space-x-2 mb-2">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <strong>API Base URL:</strong>
          </div>
          <code className="bg-white px-3 py-1 rounded-lg text-blue-600 font-mono text-xs">
            {import.meta.env.VITE_API_BASE_URL || 'https://localhost:7126/api'}
          </code>
        </div>
        <div className="text-xs text-gray-500 mt-2">
          💡 Có thể thay đổi trong file .env với biến VITE_API_BASE_URL
        </div>
      </div>
      
      {/* Endpoint Selection */}
      <div className="mb-6">
        <label className="block text-sm font-semibold text-gray-700 mb-3">
          🎯 Chọn API Endpoint
        </label>
        <select
          value={selectedEndpoint ? `${selectedEndpoint.method}:${selectedEndpoint.path}` : ''}
          onChange={(e) => {
            const [method, path] = e.target.value.split(':');
            const endpoint = endpoints.find(ep => ep.method === method && ep.path === path);
            handleEndpointChange(endpoint);
          }}
          className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white"
        >
          <option value="">-- Chọn endpoint để test --</option>
          {endpoints.map((endpoint, index) => (
            <option key={index} value={`${endpoint.method}:${endpoint.path}`}>
              {endpoint.method} {endpoint.path} - {endpoint.description}
            </option>
          ))}
        </select>
      </div>

      {/* Request Body */}
      {selectedEndpoint && selectedEndpoint.method !== 'GET' && (
        <div className="mb-6">
          <label className="block text-sm font-semibold text-gray-700 mb-3">
            📝 Request Body (JSON)
          </label>
          <div className="mb-3 p-3 bg-amber-50 border border-amber-200 rounded-xl text-sm text-amber-700">
            <div className="flex items-center space-x-2">
              <span>⚠️</span>
              <strong>Lưu ý:</strong>
            </div>
            <p className="mt-1">API yêu cầu request body phải có format: <code className="bg-white px-2 py-1 rounded">{"{ request: { ... } }"}</code></p>
          </div>
          <textarea
            value={requestBody}
            onChange={(e) => setRequestBody(e.target.value)}
            className="w-full h-40 px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white font-mono text-sm"
            placeholder="Nhập JSON request body..."
          />
        </div>
      )}

      {/* Execute Button */}
      <div className="mb-6 flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-3">
        <button
          onClick={executeRequest}
          disabled={!selectedEndpoint || loading}
          className="bg-gradient-to-r from-green-600 to-emerald-600 text-white px-6 py-3 rounded-xl hover:from-green-700 hover:to-emerald-700 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2 font-semibold shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
        >
          {loading ? (
            <>
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
              <span>Đang thực hiện...</span>
            </>
          ) : (
            <>
              <Play className="h-5 w-5" />
              <span>Thực hiện Test</span>
            </>
          )}
        </button>

        {response && (
          <button
            onClick={() => setResponse(null)}
            className="bg-gradient-to-r from-gray-500 to-gray-600 text-white px-6 py-3 rounded-xl hover:from-gray-600 hover:to-gray-700 transition-all duration-200 flex items-center space-x-2 font-semibold shadow-lg hover:shadow-xl"
          >
            <X className="h-5 w-5" />
            <span>Xóa kết quả</span>
          </button>
        )}
      </div>

      {/* Response */}
      {response && (
        <div className="border border-gray-200 rounded-2xl shadow-lg overflow-hidden">
          <div className={`flex items-center justify-between p-4 border-b ${
            response.success
              ? 'bg-gradient-to-r from-green-50 to-emerald-50 border-green-200'
              : 'bg-gradient-to-r from-red-50 to-pink-50 border-red-200'
          }`}>
            <div className="flex items-center space-x-3">
              {response.success ? (
                <div className="flex items-center space-x-2">
                  <Check className="h-6 w-6 text-green-600" />
                  <span className="font-semibold text-green-700">✅ Thành công</span>
                </div>
              ) : (
                <div className="flex items-center space-x-2">
                  <X className="h-6 w-6 text-red-600" />
                  <span className="font-semibold text-red-700">❌ Lỗi</span>
                </div>
              )}
              <span className="text-sm text-gray-500 bg-white px-2 py-1 rounded-lg">
                {new Date(response.timestamp).toLocaleTimeString()}
              </span>
            </div>
            <button
              onClick={copyResponse}
              className="text-gray-600 hover:text-gray-800 bg-white hover:bg-gray-50 p-2 rounded-lg transition-colors"
              title="Copy response"
            >
              {copied ? <Check className="h-4 w-4 text-green-600" /> : <Copy className="h-4 w-4" />}
            </button>
          </div>
          <div className="bg-gray-900 text-gray-100">
            <pre className="p-4 text-sm overflow-auto max-h-96 font-mono">
              {JSON.stringify(response, null, 2)}
            </pre>
          </div>
        </div>
      )}
    </div>
  );
};

export default ApiTester;
