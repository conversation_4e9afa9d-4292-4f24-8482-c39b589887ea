// API Base URL - c<PERSON> thể thay đổi qua environment variables
const API_BASE = import.meta.env.VITE_API_BASE_URL || 'https://localhost:7126/api';

export const apiCall = async (endpoint, options = {}) => {
  try {
    // Ensure endpoint starts with /
    const cleanEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;

    // Get token from localStorage if available
    const token = localStorage.getItem('authToken');

    const headers = {
      'Content-Type': 'application/json',
      ...options.headers
    };

    // Add Authorization header if token exists
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    const response = await fetch(`${API_BASE}${cleanEndpoint}`, {
      headers,
      ...options
    });

    // Check if response is ok (200-299)
    if (!response.ok) {
      // Try to get error details from response
      let errorData;
      try {
        errorData = await response.json();
        console.log('API Error Response:', errorData);
      } catch {
        errorData = { error: `HTTP error! status: ${response.status}` };
      }

      // Handle specific status codes
      if (response.status === 401) {
        // Unauthorized - token expired or invalid
        localStorage.removeItem('authToken');
        localStorage.removeItem('userInfo');
        throw new Error('Phiên đăng nhập đã hết hạn. Vui lòng đăng nhập lại.');
      }

      if (response.status === 403) {
        throw new Error('Bạn không có quyền truy cập tính năng này.');
      }

      if (response.status === 404) {
        throw new Error('Không tìm thấy dữ liệu yêu cầu.');
      }

      if (response.status >= 500) {
        throw new Error('Lỗi máy chủ. Vui lòng thử lại sau.');
      }

      // Create detailed error message
      let errorMessage = `HTTP ${response.status}`;

      if (errorData.title) {
        errorMessage += `: ${errorData.title}`;
      }

      if (errorData.errors) {
        const errorDetails = [];
        for (const [field, messages] of Object.entries(errorData.errors)) {
          if (Array.isArray(messages)) {
            errorDetails.push(`${field}: ${messages.join(', ')}`);
          } else {
            errorDetails.push(`${field}: ${messages}`);
          }
        }
        if (errorDetails.length > 0) {
          errorMessage += `\nChi tiết: ${errorDetails.join('; ')}`;
        }
      }

      if (errorData.error || errorData.message) {
        errorMessage += `\n${errorData.error || errorData.message}`;
      }

      // Create error object with full details
      const error = new Error(errorMessage);
      error.status = response.status;
      error.details = errorData;
      throw error;
    }

    // Return JSON response
    return await response.json();
  } catch (error) {
    console.error('API call error:', error);
    throw error;
  }
};
