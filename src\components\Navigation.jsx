import React from 'react';
import { Mail, Filter, Search, Eye, BarChart3, Settings } from 'lucide-react';

const Navigation = ({ activeTab, setActiveTab }) => {
  const tabs = [
    { id: 'accounts', label: '<PERSON><PERSON><PERSON>n', icon: Mail },
    { id: 'filters', label: 'Bộ lọc', icon: Filter },
    { id: 'scan', label: 'Quét Email', icon: Search },
    { id: 'data', label: 'Kết quả', icon: Eye },
    { id: 'api-test', label: 'API Test', icon: Settings },
  ];

  return (
    <div className="w-full bg-white border-b">
      <div className="w-full px-8">
        <nav className="flex space-x-8">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center space-x-2 px-4 py-4 border-b-2 font-medium transition-colors ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Icon className="h-5 w-5" />
                <span>{tab.label}</span>
              </button>
            );
          })}
        </nav>
      </div>
    </div>
  );
};

export default Navigation;