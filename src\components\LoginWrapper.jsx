import React, { useState } from 'react';
import { ArrowLeft } from 'lucide-react';
import LoginForm from './LoginForm';
import ApiTester from './ApiTester';

const LoginWrapper = ({ onLogin }) => {
  const [showApiTester, setShowApiTester] = useState(false);

  if (showApiTester) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 p-4">
        <div className="max-w-5xl mx-auto">
          {/* Header với nút back */}
          <div className="mb-8">
            <button
              onClick={() => setShowApiTester(false)}
              className="inline-flex items-center space-x-2 text-blue-600 hover:text-blue-800 transition-all duration-200 bg-white hover:bg-blue-50 px-4 py-2 rounded-xl shadow-md hover:shadow-lg font-medium"
            >
              <ArrowLeft className="h-4 w-4" />
              <span>Quay lại đăng nhập</span>
            </button>
          </div>

          {/* API Tester */}
          <ApiTester />

          {/* Thông tin */}
          <div className="mt-8 text-center">
            <div className="bg-white rounded-xl p-6 shadow-lg border border-gray-100">
              <div className="text-sm text-gray-600">
                <p className="font-semibold text-gray-700 mb-2">💡 Hướng dẫn sử dụng</p>
                <p>Sử dụng API Tester để kiểm tra kết nối với server</p>
                <p className="mt-1">Sau khi kiểm tra xong, quay lại để đăng nhập</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <LoginForm 
      onLogin={onLogin} 
      onShowApiTester={() => setShowApiTester(true)} 
    />
  );
};

export default LoginWrapper;
