import React, { useState } from 'react';
import { Mail, Lock, Eye, EyeOff, Settings, X } from 'lucide-react';
import { apiCall } from '../utils/api';
import { authUtils } from '../utils/auth';

const LoginForm = ({ onLogin, onShowApiTester }) => {
  const [credentials, setCredentials] = useState({
    username: '',
    password: ''
  });
  const [showPassword, setShowPassword] = useState(false);
  const [rememberMe, setRememberMe] = useState(true); // Mặc định check remember me
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      const response = await apiCall('/Auth/login', {
        method: 'POST',
        body: JSON.stringify({
          request: {
            email: credentials.username, // <PERSON> có thể expect email thay vì username
            password: credentials.password
          }
        })
      });

      // Kiểm tra response có token không
      if (response && (response.token || response.accessToken || response.jwt)) {
        const token = response.token || response.accessToken || response.jwt;
        authUtils.setToken(token);

        // Lưu thông tin user nếu có
        if (response.user) {
          localStorage.setItem('userInfo', JSON.stringify(response.user));
        }

        // Lưu trạng thái remember me
        if (rememberMe) {
          localStorage.setItem('rememberLogin', 'true');
        } else {
          localStorage.removeItem('rememberLogin');
        }

        onLogin();
      } else {
        setError('Đăng nhập thất bại - Không nhận được token');
      }
    } catch (error) {
      console.error('Login error:', error);
      setError(error.message || 'Lỗi đăng nhập');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 flex items-center justify-center p-4 relative overflow-hidden">
      {/* Background decoration */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-400/20 to-indigo-400/20 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-purple-400/20 to-pink-400/20 rounded-full blur-3xl"></div>
      </div>

      <div className="bg-white/80 backdrop-blur-lg rounded-2xl shadow-2xl p-6 sm:p-8 w-full max-w-md border border-white/20 relative z-10 animate-in slide-in-from-bottom-4 duration-700">
        <div className="text-center mb-8">
          <div className="bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 p-4 rounded-2xl inline-block mb-6 shadow-lg">
            <Mail className="h-10 w-10 text-white" />
          </div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent mb-3">
            📧 LSB Email Manager
          </h1>
          <p className="text-gray-600 text-lg">👋 Chào mừng! Vui lòng đăng nhập để bắt đầu</p>
          <div className="mt-2 text-sm text-gray-500">
            ⚡ Quản lý và theo dõi email một cách hiệu quả
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-3">
              📧 Email
            </label>
            <div className="relative">
              <input
                type="email"
                value={credentials.username}
                onChange={(e) => setCredentials({...credentials, username: e.target.value})}
                className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white hover:border-gray-400 hover:shadow-md"
                placeholder="Nhập email của bạn"
                required
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-semibold text-gray-700 mb-3">
              🔒 Mật khẩu
            </label>
            <div className="relative">
              <input
                type={showPassword ? 'text' : 'password'}
                value={credentials.password}
                onChange={(e) => setCredentials({...credentials, password: e.target.value})}
                className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white hover:border-gray-400 hover:shadow-md pr-12"
                placeholder="Nhập mật khẩu"
                required
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
              >
                {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
              </button>
            </div>
          </div>

          {/* Remember Me */}
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <input
                id="remember-me"
                type="checkbox"
                checked={rememberMe}
                onChange={(e) => setRememberMe(e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded transition-colors"
              />
              <label htmlFor="remember-me" className="ml-3 block text-sm font-medium text-gray-700">
                Ghi nhớ đăng nhập
              </label>
            </div>
          </div>

          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-xl flex items-center space-x-2">
              <X className="h-4 w-4 flex-shrink-0" />
              <span className="text-sm">{error}</span>
            </div>
          )}

          <button
            type="submit"
            disabled={loading}
            className="w-full bg-gradient-to-r from-blue-600 to-indigo-600 text-white py-3 px-6 rounded-xl hover:from-blue-700 hover:to-indigo-700 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2 font-semibold shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
          >
            {loading ? (
              <>
                <div className="flex space-x-1">
                  <div className="w-2 h-2 bg-white rounded-full animate-bounce"></div>
                  <div className="w-2 h-2 bg-white rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                  <div className="w-2 h-2 bg-white rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                </div>
                <span>Đang đăng nhập...</span>
              </>
            ) : (
              <>
                <Lock className="h-5 w-5" />
                <span>Đăng nhập</span>
              </>
            )}
          </button>
        </form>

        {/* Thông tin hướng dẫn và API Tester */}
        <div className="mt-8 text-center space-y-4">
          <div className="text-xs text-gray-500 bg-gray-50 rounded-lg p-3">
            <p className="font-medium">Lần đầu sử dụng?</p>
            <p className="mt-1">Liên hệ quản trị viên để được cấp tài khoản</p>
          </div>

          {onShowApiTester && (
            <button
              onClick={onShowApiTester}
              className="inline-flex items-center space-x-2 text-sm text-blue-600 hover:text-blue-800 transition-all duration-200 bg-blue-50 hover:bg-blue-100 px-4 py-2 rounded-lg font-medium"
            >
              <Settings className="h-4 w-4" />
              <span>Kiểm tra kết nối API</span>
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default LoginForm;