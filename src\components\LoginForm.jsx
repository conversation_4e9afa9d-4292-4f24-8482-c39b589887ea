import React, { useState } from 'react';
import { Mail, Lock, Eye, EyeOff, Settings } from 'lucide-react';

const LoginForm = ({ onLogin, onShowApiTester }) => {
  const [credentials, setCredentials] = useState({
    username: '',
    password: ''
  });
  const [showPassword, setShowPassword] = useState(false);
  const [rememberMe, setRememberMe] = useState(true);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    // Simulate API call
    try {
      await new Promise(resolve => setTimeout(resolve, 1500));
      // Mock successful login
      onLogin && onLogin();
    } catch (error) {
      setError('Lỗi đăng nhập');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
      {/* Card container chứa form - căn giữa hoàn toàn */}
      <div className="bg-white rounded-2xl shadow-xl p-10 w-full max-w-lg mx-4">
        {/* Header section */}
        <div className="text-center mb-10">
          <div className="p-4 rounded-xl inline-block mb-6">
            {/* Logo LSB - thay thế bằng đường dẫn thực tế */}
            <img 
              src="/src/assets/LSB.png" 
              alt="LSB Logo" 
              className="h-20 w-20 object-contain mx-auto"
              onError={(e) => {
                // Fallback về icon Mail nếu không load được logo
                e.target.style.display = 'none';
                e.target.nextSibling.style.display = 'block';
              }}
            />
            <Mail className="h-20 w-20 text-blue-600 mx-auto" style={{display: 'none'}} />
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-3">
            LSB Email Manager
          </h1>
          <p className="text-gray-600 text-base">
            Chào mừng! Vui lòng đăng nhập để bắt đầu
          </p>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="space-y-7">
          {/* Email field */}
          <div>
            <label className="block text-base font-medium text-gray-700 mb-2">
              Email
            </label>
            <input
              type="email"
              value={credentials.username}
              onChange={(e) => setCredentials({...credentials, username: e.target.value})}
              className="w-full px-5 py-4 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-base transition-all duration-200"
              placeholder="Nhập email của bạn"
              required
            />
          </div>

          {/* Password field */}
          <div>
            <label className="block text-base font-medium text-gray-700 mb-2">
              Mật khẩu
            </label>
            <div className="relative">
              <input
                type={showPassword ? 'text' : 'password'}
                value={credentials.password}
                onChange={(e) => setCredentials({...credentials, password: e.target.value})}
                className="w-full px-5 py-4 pr-14 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-base transition-all duration-200"
                placeholder="Nhập mật khẩu"
                required
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-5 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
              >
                {showPassword ? 
                  <EyeOff className="h-5 w-5" /> : 
                  <Eye className="h-5 w-5" />
                }
              </button>
            </div>
          </div>

          {/* Remember Me checkbox */}
          <div className="flex items-center">
            <input
              id="remember-me"
              type="checkbox"
              checked={rememberMe}
              onChange={(e) => setRememberMe(e.target.checked)}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded transition-all"
            />
            <label htmlFor="remember-me" className="ml-3 block text-base text-gray-700 select-none">
              Ghi nhớ đăng nhập
            </label>
          </div>

          {/* Error message */}
          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg text-sm">
              {error}
            </div>
          )}

          {/* Submit button */}
          <button
            type="submit"
            disabled={loading}
            className="w-full bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 active:bg-blue-800 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2 font-medium text-base"
          >
            {loading ? (
              <>
                <div className="animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent"></div>
                <span>Đang đăng nhập...</span>
              </>
            ) : (
              <>
                <Lock className="h-5 w-5" />
                <span>Đăng nhập</span>
              </>
            )}
          </button>
        </form>

        {/* Footer section */}
        <div className="mt-10 text-center space-y-5">
          <div className="text-base text-gray-500">
            <p>Lần đầu sử dụng? Liên hệ quản trị viên để được cấp tài khoản</p>
          </div>

          {onShowApiTester && (
            <button
              onClick={onShowApiTester}
              className="inline-flex items-center space-x-2 text-base text-blue-600 hover:text-blue-800 transition-colors py-2 px-3 rounded-lg hover:bg-blue-50"
            >
              <Settings className="h-5 w-5" />
              <span>Kiểm tra kết nối API</span>
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default LoginForm;