import React, { useState, useEffect } from 'react';
import { authUtils } from './utils/auth';
import LSBSellerMailTrack from './components/LSBSellerMailTrack';
import LoginWrapper from './components/LoginWrapper';

function App() {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [loading, setLoading] = useState(false); // Không loading ban đầu

  useEffect(() => {
    // Kiểm tra authentication khi app khởi động
    const checkAuth = async () => {
      const token = authUtils.getToken();
      const rememberLogin = localStorage.getItem('rememberLogin');

      // Nếu không có token hoặc user không chọn "remember me", hiển thị login form
      if (!token || !rememberLogin) {
        setIsAuthenticated(false);
        setLoading(false);
        // Xóa token nếu user không muốn remember
        if (!rememberLogin && token) {
          authUtils.removeToken();
        }
        return;
      }

      // N<PERSON>u có token và user chọn remember, kiểm tra tính hợp lệ
      setLoading(true);
      try {
        const isValid = await authUtils.validateToken();
        setIsAuthenticated(isValid);
        if (!isValid) {
          // Token không hợp lệ, xóa tất cả
          authUtils.removeToken();
          localStorage.removeItem('rememberLogin');
        }
      } catch (error) {
        console.error('Auth check error:', error);
        setIsAuthenticated(false);
        // Xóa token và remember state không hợp lệ
        authUtils.removeToken();
        localStorage.removeItem('rememberLogin');
      } finally {
        setLoading(false);
      }
    };

    checkAuth();
  }, []);

  const handleLogin = () => {
    setIsAuthenticated(true);
  };

  const handleLogout = () => {
    setIsAuthenticated(false);
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Đang kiểm tra phiên đăng nhập...</p>
        </div>
      </div>
    );
  }

  return isAuthenticated ?
    <LSBSellerMailTrack onLogout={handleLogout} /> :
    <LoginWrapper onLogin={handleLogin} />;
}

export default App;
