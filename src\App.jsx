import React, { useState, useEffect } from 'react';
import { authUtils } from './utils/auth';
import LSBSellerMailTrack from './components/LSBSellerMailTrack';
import LoginForm from './components/LoginForm';

function App() {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Kiểm tra authentication khi app khởi động
    const checkAuth = async () => {
      try {
        // Kiểm tra token có hợp lệ không
        const isValid = await authUtils.validateToken();
        setIsAuthenticated(isValid);
      } catch (error) {
        console.error('Auth check error:', error);
        setIsAuthenticated(false);
      } finally {
        setLoading(false);
      }
    };

    checkAuth();
  }, []);

  const handleLogin = () => {
    setIsAuthenticated(true);
  };

  const handleLogout = () => {
    setIsAuthenticated(false);
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Đang tải...</p>
        </div>
      </div>
    );
  }

  return isAuthenticated ? 
    <LSBSellerMailTrack onLogout={handleLogout} /> : 
    <LoginForm onLogin={handleLogin} />;
}

export default App;
