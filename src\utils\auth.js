import { apiCall } from './api';

export const authUtils = {
  // Ki<PERSON>m tra xem user đã đăng nhập chưa
  isAuthenticated: () => {
    const token = localStorage.getItem('authToken');
    return !!token;
  },

  // <PERSON><PERSON><PERSON> tra token có hợp lệ không
  validateToken: async () => {
    const token = localStorage.getItem('authToken');
    if (!token) return false;

    try {
      // Sử dụng endpoint có sẵn để kiểm tra token
      await apiCall('/Auth/refresh-jwt-token', {
        method: 'POST',
        body: JSON.stringify({
          request: {
            refreshToken: token
          }
        })
      });
      return true;
    } catch (error) {
      // Token không hợp lệ, xóa nó
      authUtils.removeToken();
      return false;
    }
  },

  // Lấy token từ localStorage
  getToken: () => {
    return localStorage.getItem('authToken');
  },

  // <PERSON><PERSON><PERSON> token vào localStorage
  setToken: (token) => {
    localStorage.setItem('authToken', token);
  },

  // Xóa token khỏi localStorage
  removeToken: () => {
    localStorage.removeItem('authToken');
    localStorage.removeItem('userInfo');
    localStorage.removeItem('rememberLogin');
  },

  // Đăng xuất
  logout: async () => {
    try {
      await apiCall('/Auth/logout', {
        method: 'POST',
        body: JSON.stringify({
          request: {}
        })
      });
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      authUtils.removeToken();
      // Reload trang để reset state
      window.location.href = '/';
    }
  },

  // Refresh token
  refreshToken: async (accountId) => {
    try {
      const response = await apiCall(`/Auth/refresh-jwt-token/${accountId}`, {
        method: 'POST',
        body: JSON.stringify({
          request: {
            refreshToken: authUtils.getToken()
          }
        })
      });
      if (response.token) {
        authUtils.setToken(response.token);
        return response.token;
      }
    } catch (error) {
      console.error('Refresh token error:', error);
      authUtils.removeToken();
      throw error;
    }
  }
};
