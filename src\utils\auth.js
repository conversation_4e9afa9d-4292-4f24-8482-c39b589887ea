import { apiCall } from './api';

export const authUtils = {
  // Ki<PERSON>m tra xem user đã đăng nhập chưa
  isAuthenticated: () => {
    const token = localStorage.getItem('authToken');
    return !!token;
  },

  // <PERSON><PERSON><PERSON> tra token có hợp lệ không
  validateToken: async () => {
    const token = localStorage.getItem('authToken');
    if (!token) return false;

    try {
      // Sử dụng endpoint có sẵn để kiểm tra token
      await apiCall('/auth/refresh-jwt-token', { method: 'POST' });
      return true;
    } catch (error) {
      // Token không hợp lệ, xóa nó
      authUtils.removeToken();
      return false;
    }
  },

  // Lấy token từ localStorage
  getToken: () => {
    return localStorage.getItem('authToken');
  },

  // Lưu token vào localStorage
  setToken: (token) => {
    localStorage.setItem('authToken', token);
  },

  // Xóa token khỏi localStorage
  removeToken: () => {
    localStorage.removeItem('authToken');
  },

  // Đăng xuất
  logout: async () => {
    try {
      await apiCall('/auth/logout', { method: 'POST' });
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      authUtils.removeToken();
      // Reload trang để reset state
      window.location.href = '/';
    }
  },

  // Refresh token
  refreshToken: async (accountId) => {
    try {
      const response = await apiCall(`/auth/refresh-jwt-token/${accountId}`, { method: 'POST' });
      if (response.token) {
        authUtils.setToken(response.token);
        return response.token;
      }
    } catch (error) {
      console.error('Refresh token error:', error);
      authUtils.removeToken();
      throw error;
    }
  }
};
