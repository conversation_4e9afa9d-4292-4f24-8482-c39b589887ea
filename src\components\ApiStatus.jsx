import React, { useState, useEffect } from 'react';
import { Wifi, WifiOff, RefreshCw } from 'lucide-react';
import { apiCall } from '../utils/api';

const ApiStatus = () => {
  const [status, setStatus] = useState('checking'); // 'checking', 'connected', 'disconnected'
  const [lastCheck, setLastCheck] = useState(null);

  const checkApiStatus = async () => {
    setStatus('checking');
    try {
      // Thử gọi một endpoint đơn giản để kiểm tra kết nối
      await apiCall('/Auth/refresh-jwt-token', { method: 'POST' });
      setStatus('connected');
    } catch (error) {
      console.error('API Status check failed:', error);
      setStatus('disconnected');
    }
    setLastCheck(new Date());
  };

  useEffect(() => {
    checkApiStatus();
    // Kiểm tra mỗi 30 giây
    const interval = setInterval(checkApiStatus, 30000);
    return () => clearInterval(interval);
  }, []);

  const getStatusColor = () => {
    switch (status) {
      case 'connected': return 'text-green-600';
      case 'disconnected': return 'text-red-600';
      default: return 'text-yellow-600';
    }
  };

  const getStatusIcon = () => {
    switch (status) {
      case 'connected': return <Wifi className="h-4 w-4" />;
      case 'disconnected': return <WifiOff className="h-4 w-4" />;
      default: return <RefreshCw className="h-4 w-4 animate-spin" />;
    }
  };

  const getStatusText = () => {
    switch (status) {
      case 'connected': return 'Kết nối API';
      case 'disconnected': return 'Mất kết nối API';
      default: return 'Đang kiểm tra...';
    }
  };

  return (
    <div className="flex items-center space-x-2">
      <div className={`flex items-center space-x-1 ${getStatusColor()}`}>
        {getStatusIcon()}
        <span className="text-xs">{getStatusText()}</span>
      </div>
      {lastCheck && (
        <span className="text-xs text-gray-500">
          {lastCheck.toLocaleTimeString()}
        </span>
      )}
      <button
        onClick={checkApiStatus}
        className="text-xs text-blue-600 hover:text-blue-800"
        title="Kiểm tra lại"
      >
        <RefreshCw className="h-3 w-3" />
      </button>
    </div>
  );
};

export default ApiStatus;
