# LSB Seller Mail Tracker UI

Ứng dụng quản lý và theo dõi email cho LSB Seller với tích hợp API đầy đủ.

## Tính năng

- 🔐 **Đăng nhập bảo mật** với JWT token
- 📧 **Quản lý tài khoản email** (thêm, sửa, xóa)
- 🔍 **Quét và phân tích email**
- 📊 **Hiển thị kết quả** với bảng dữ liệu
- 🛠️ **API Tester** để test các endpoints
- 📱 **Responsive design** với Tailwind CSS
- ⚡ **Real-time API status** monitoring

## Cài đặt

1. Clone repository:
```bash
git clone <repository-url>
cd lsb-seller-mail-tracker-ui
```

2. Cài đặt dependencies:
```bash
npm install
```

3. Cấu hình environment:
```bash
cp .env.example .env
```

Chỉnh sửa file `.env` với thông tin API của bạn:
```
VITE_API_BASE_URL=https://localhost:7126/api
```

4. Chạy ứng dụng:
```bash
npm run dev
```

## API Integration

Ứng dụng tích hợp với các API endpoints sau:

### Authentication
- `POST /api/Auth/login` - Đăng nhập
- `POST /api/Auth/logout` - Đăng xuất
- `POST /api/Auth/refresh-jwt-token/{accountId}` - Refresh token

### Account Management
- `GET /api/Accounts` - Lấy danh sách tài khoản
- `POST /api/Accounts` - Tạo tài khoản mới
- `PUT /api/Accounts/{id}` - Cập nhật tài khoản
- `DELETE /api/Accounts/{id}` - Xóa tài khoản

## Cấu trúc dự án

```
src/
├── components/          # React components
│   ├── LoginForm.jsx   # Form đăng nhập
│   ├── LSBSellerMailTrack.jsx  # Component chính
│   ├── Navigation.jsx  # Thanh điều hướng
│   ├── AccountsTab.jsx # Tab quản lý tài khoản
│   ├── FiltersTab.jsx  # Tab bộ lọc
│   ├── ScanTab.jsx     # Tab quét email
│   ├── DataTab.jsx     # Tab hiển thị kết quả
│   ├── ApiStatus.jsx   # Hiển thị trạng thái API
│   └── ApiTester.jsx   # Tool test API
├── utils/              # Utilities
│   ├── api.js         # API client
│   └── auth.js        # Authentication utilities
├── App.jsx            # App component chính
└── main.jsx          # Entry point
```

## Sử dụng

1. **Đăng nhập**: Nhập email và mật khẩu để đăng nhập
2. **Quản lý tài khoản**: Thêm, sửa, xóa tài khoản email
3. **Thiết lập bộ lọc**: Cấu hình các bộ lọc email
4. **Quét email**: Thực hiện quét và phân tích email
5. **Xem kết quả**: Kiểm tra dữ liệu đã trích xuất
6. **Test API**: Sử dụng tab API Test để kiểm tra các endpoints

## Công nghệ sử dụng

- **React 18** - UI framework
- **Vite** - Build tool
- **Tailwind CSS** - Styling
- **Lucide React** - Icons
- **Ant Design** - UI components

## Development

```bash
# Chạy development server
npm run dev

# Build cho production
npm run build

# Preview production build
npm run preview

# Lint code
npm run lint
```
